import { useState, useEffect, useRef } from 'react';
import Image from 'next/image';
import { useLanguage } from '../lib/LanguageContext';

export default function LanguageSelector() {
  const { language, setLanguage } = useLanguage();
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  const getFlagPath = (lang: string) => {
    return `/flags/${lang === 'en' ? 'gb' : lang}.svg`;
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 px-2 py-1 rounded-lg hover:bg-zinc-800 transition"
      >
        <Image
          src={`/flags/${language}.svg`}
          alt={language === 'ru' ? 'Russian' : 'English'}
          width={24}
          height={16}
          className="w-6 h-4 object-cover rounded"
        />
        <svg
          className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-2 py-2 w-32 bg-zinc-900 rounded-lg shadow-xl border border-zinc-800">
          <button
            onClick={() => {
              setLanguage('ru');
              setIsOpen(false);
            }}
            className={`flex items-center space-x-3 w-full px-4 py-2 text-left hover:bg-zinc-800 transition ${
              language === 'ru' ? 'text-blue-400' : ''
            }`}
          >
            <Image
              src="/flags/ru.svg"
              alt="Russian"
              width={24}
              height={16}
              className="w-6 h-4 object-cover rounded"
            />
            <span>Русский</span>
          </button>
          <button
            onClick={() => {
              setLanguage('gb');
              setIsOpen(false);
            }}
            className={`flex items-center space-x-3 w-full px-4 py-2 text-left hover:bg-zinc-800 transition ${
              language === 'gb' ? 'text-blue-400' : ''
            }`}
          >
            <Image
              src="/flags/gb.svg"
              alt="English"
              width={24}
              height={16}
              className="w-6 h-4 object-cover rounded"
            />
            <span>English</span>
          </button>
        </div>
      )}
    </div>
  );
} 